"""
Proxy Configuration for NSE and BSE Scrapers

This file allows you to easily configure ScraperAPI proxy settings.
You can enable/disable proxy usage and customize ScraperAPI parameters.
Supports environment variables for cloud deployment.
"""

import os

# ScraperAPI Configuration with environment variable support
SCRAPER<PERSON>I_CONFIG = {
    # Enable or disable proxy usage (can be overridden by environment variable)
    # TEMPORARILY DISABLED due to 401 Unauthorized error with current API key
    "USE_PROXY": os.getenv("USE_PROXY", "false").lower() == "true",

    # Your ScraperAPI key (can be overridden by environment variable)
    # Current key returns 401 Unauthorized - needs to be updated with valid key
    "API_KEY": os.getenv("SCRAPERAPI_KEY", "********************************"),

    # ScraperAPI endpoints
    "ENDPOINT": "https://api.scraperapi.com",  # Use HTTPS like working script
    "PROXY_HOST": "proxy-server.scraperapi.com",
    "PROXY_PORT": 8001,

    # Scraper<PERSON>I parameters for enhanced scraping
    "RENDER_JS": False,  # Disable JS rendering for API calls (like working script)
    "COUNTRY_CODE": "IN",  # Use Indian proxies for better access to Indian sites
    "PREMIUM": True,  # Use premium proxies (like working script)

    # Additional ScraperAPI parameters (optional)
    "DEVICE_TYPE": None,  # "desktop" or "mobile"
    "SESSION_NUMBER": None,  # For session persistence
    "KEEP_HEADERS": True,  # Keep original headers
}

# Proxy mode selection
# "endpoint" - Use ScraperAPI endpoint (recommended for complex sites, like working script)
# "proxy" - Use ScraperAPI as HTTP proxy (faster for simple requests)
PROXY_MODE = "endpoint"  # Use endpoint mode like working script

def get_proxy_config():
    """
    Get the current proxy configuration.
    
    Returns:
        dict: Proxy configuration dictionary
    """
    return SCRAPERAPI_CONFIG.copy()

def is_proxy_enabled():
    """
    Check if proxy is enabled.
    
    Returns:
        bool: True if proxy is enabled
    """
    return SCRAPERAPI_CONFIG.get("USE_PROXY", False)

def get_proxy_mode():
    """
    Get the current proxy mode.
    
    Returns:
        str: "endpoint" or "proxy"
    """
    return PROXY_MODE

def update_proxy_config(**kwargs):
    """
    Update proxy configuration.
    
    Args:
        **kwargs: Configuration parameters to update
    """
    SCRAPERAPI_CONFIG.update(kwargs)
    print(f"Proxy configuration updated: {kwargs}")

def disable_proxy():
    """Disable proxy usage"""
    SCRAPERAPI_CONFIG["USE_PROXY"] = False
    print("Proxy disabled")

def enable_proxy():
    """Enable proxy usage"""
    SCRAPERAPI_CONFIG["USE_PROXY"] = True
    print("Proxy enabled")

# Usage examples:
if __name__ == "__main__":
    print("Current proxy config:", get_proxy_config())
    print("Proxy enabled:", is_proxy_enabled())
    print("Proxy mode:", get_proxy_mode())
