#!/usr/bin/env python3
"""
Calculate PDF hashes for all existing records
This script processes all existing records and calculates PDF hashes using the Edge Function.
"""

import requests
import json
import time
import argparse
import sys
import hashlib
import io
from datetime import datetime
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import threading
try:
    import PyPDF2
    PDF_SUPPORT = True
except ImportError:
    PDF_SUPPORT = False

# Configuration
SUPABASE_URL = "https://yvuwseolouiqhoxsieop.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl2dXdzZW9sb3VpcWhveHNpZW9wIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI1OTI2ODcsImV4cCI6MjA2ODE2ODY4N30.W7m701xSGJ5eh8oc4turmb4zO9-nz1Pbzqz-DL9EEow"

class PDFHashCalculator:
    """Calculator for PDF hashes using Edge Functions or local processing"""

    def __init__(self, supabase_key=None, use_local=False):
        """Initialize with Supabase key and processing mode"""
        self.supabase_url = SUPABASE_URL
        self.supabase_key = SUPABASE_KEY
        self.use_local = use_local
        self.headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {SUPABASE_KEY}',
            'apikey': SUPABASE_KEY
        }
        self.stats = {
            'total_processed': 0,
            'successful': 0,
            'failed': 0,
            'skipped': 0,
            'duplicates_found': 0,
            'pdf_404_errors': 0,
            'pdf_invalid_format': 0,
            'pdf_no_text': 0,
            'pdf_encrypted': 0
        }
        self.stats_lock = threading.Lock()

        # Create a session for better connection handling
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/pdf,application/octet-stream,*/*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive'
        })

        # Check PDF support for local processing
        if use_local and not PDF_SUPPORT:
            print("⚠️ PyPDF2 not installed. Install with: pip install PyPDF2")
            print("⚠️ Falling back to Edge Function processing")
            self.use_local = False

    def calculate_pdf_hash_local(self, pdf_url):
        """Calculate PDF hash locally by downloading and processing the PDF"""
        try:
            # Download PDF using session with shorter timeout
            response = self.session.get(pdf_url, timeout=30, allow_redirects=True)
            if not response.ok:
                return None, f"Failed to download PDF: HTTP {response.status_code} - {pdf_url}"

            # Check if it's actually a PDF
            if not response.content.startswith(b'%PDF'):
                # Check if it might be an HTML error page
                content_str = response.content.decode('utf-8', errors='ignore')[:500]
                if '<html' in content_str.lower() or '<body' in content_str.lower():
                    return None, f"Downloaded HTML instead of PDF (likely error page): {content_str[:100]}..."
                else:
                    return None, f"Downloaded file is not a valid PDF (starts with: {response.content[:20]})"

            # Create PDF reader
            pdf_file = io.BytesIO(response.content)
            try:
                pdf_reader = PyPDF2.PdfReader(pdf_file)
            except Exception as e:
                return None, f"Failed to read PDF file: {str(e)}"

            # Check if PDF is encrypted or has no pages
            if pdf_reader.is_encrypted:
                return None, "PDF is encrypted and cannot be processed"

            if len(pdf_reader.pages) == 0:
                return None, "PDF has no pages"

            # Extract text from all pages
            text_content = ""
            for page_num, page in enumerate(pdf_reader.pages):
                try:
                    page_text = page.extract_text()
                    if page_text:
                        text_content += page_text + "\n"
                except Exception as e:
                    # Log the error but continue with other pages
                    print(f"⚠️ Failed to extract text from page {page_num + 1}: {str(e)}")
                    continue

            # Check if we extracted any meaningful text
            if not text_content.strip():
                # If no text was extracted, use the raw PDF content for hashing
                # This handles cases like image-only PDFs or PDFs with extraction issues
                print("⚠️ No text extracted from PDF, using raw content for hashing")
                pdf_hash = hashlib.sha256(response.content).hexdigest()
                return pdf_hash, None

            # Normalize text (remove extra whitespace, convert to lowercase)
            normalized_text = ' '.join(text_content.split()).lower()

            # Calculate hash from normalized text
            pdf_hash = hashlib.sha256(normalized_text.encode('utf-8')).hexdigest()

            return pdf_hash, None

        except Exception as e:
            return None, f"Error processing PDF: {str(e)}"

    def update_record_hash(self, record_id, table_name, pdf_hash, is_duplicate=False):
        """Update a record with the calculated PDF hash and duplicate status"""
        try:
            url = f"{self.supabase_url}/rest/v1/{table_name}"

            # Update the record with hash and status
            update_data = {
                "pdf_hash": pdf_hash,
                "duplicate_check_status": "completed",
                "is_duplicate": is_duplicate
            }
            params = {"id": f"eq.{record_id}"}

            response = requests.patch(
                url,
                headers=self.headers,
                json=update_data,
                params=params,
                timeout=30
            )

            if response.ok:
                return True, None
            else:
                return False, f"HTTP {response.status_code}: {response.text[:100]}"

        except Exception as e:
            return False, f"Error updating record: {str(e)}"

    def mark_record_failed(self, record_id, table_name, error_reason):
        """Mark a record as failed processing"""
        try:
            url = f"{self.supabase_url}/rest/v1/{table_name}"

            # Update the record with failed status
            update_data = {
                "duplicate_check_status": "failed",
                "processing_error": error_reason[:500]  # Limit error message length
            }
            params = {"id": f"eq.{record_id}"}

            response = requests.patch(
                url,
                headers=self.headers,
                json=update_data,
                params=params,
                timeout=30
            )

            if response.ok:
                return True, None
            else:
                return False, f"HTTP {response.status_code}: {response.text[:100]}"

        except Exception as e:
            return False, f"Error marking record as failed: {str(e)}"

    def check_duplicate_hash(self, pdf_hash, current_record_id, table_name):
        """Check if a PDF hash already exists in the database"""
        try:
            url = f"{self.supabase_url}/rest/v1/{table_name}"

            params = {
                'select': 'id,pdf_hash',
                'pdf_hash': f'eq.{pdf_hash}',
                'id': f'neq.{current_record_id}'  # Exclude current record
            }

            response = requests.get(
                url,
                headers=self.headers,
                params=params,
                timeout=30
            )

            if response.ok:
                duplicates = response.json()
                return len(duplicates) > 0, duplicates
            else:
                print(f"⚠️ Error checking for duplicates: HTTP {response.status_code}")
                return False, []

        except Exception as e:
            print(f"⚠️ Error checking for duplicates: {str(e)}")
            return False, []
    
    def get_records_needing_hashes(self, table_name, limit=None, status_filter='pending'):
        """Get records that need PDF hash calculation"""
        try:
            # Build query parameters
            params = {
                'select': '*',
            }

            # Filter based on processing status
            if status_filter == 'pending':
                # For now, just get records where pdf_hash is null (simpler approach)
                params['pdf_hash'] = 'is.null'
            else:
                params['pdf_hash'] = 'is.null'  # Records where pdf_hash is null

            if table_name == 'bse_corporate_announcements':
                params['attachmentfile'] = 'not.is.null'  # Has attachment file
            elif table_name == 'nse_corporate_announcements':
                params['attachment_url'] = 'not.is.null'  # Has attachment URL

            if limit:
                params['limit'] = limit

            params['order'] = 'created_at.asc'  # Process oldest first
            
            url = f"{self.supabase_url}/rest/v1/{table_name}"
            
            print(f"🔍 Fetching records from {table_name} that need hash calculation...")
            
            response = requests.get(url, headers=self.headers, params=params, timeout=30)
            
            if response.ok:
                records = response.json()
                print(f"✅ Found {len(records)} records needing hash calculation in {table_name}")
                return records
            else:
                print(f"❌ Failed to fetch records from {table_name}: {response.status_code}")
                print(f"Response: {response.text}")
                return []
                
        except Exception as e:
            print(f"❌ Error fetching records from {table_name}: {e}")
            return []
    
    def calculate_single_hash(self, record, table_name, max_retries=3):
        """Calculate hash for a single record using Edge Function or local processing"""
        try:
            record_id = record['id']

            # Get PDF URL based on table
            if table_name == 'bse_corporate_announcements':
                pdf_url = record.get('attachmentfile')
            else:  # nse_corporate_announcements
                pdf_url = record.get('attachment_url')

            if not pdf_url or pdf_url.strip() == '':
                with self.stats_lock:
                    self.stats['skipped'] += 1
                return {'success': False, 'reason': 'No PDF URL'}

            # Local processing mode
            if self.use_local:
                start_time = time.time()
                print(f"🔍 Processing {record_id} locally...")

                # Calculate hash locally
                pdf_hash, error = self.calculate_pdf_hash_local(pdf_url)

                if pdf_hash:
                    # Check for duplicates
                    is_duplicate, duplicate_records = self.check_duplicate_hash(pdf_hash, record_id, table_name)

                    # Update the record in the database
                    update_success, update_error = self.update_record_hash(record_id, table_name, pdf_hash, is_duplicate)

                    if update_success:
                        processing_time = int((time.time() - start_time) * 1000)
                        with self.stats_lock:
                            self.stats['successful'] += 1
                            if is_duplicate:
                                self.stats['duplicates_found'] += 1

                        if is_duplicate:
                            print(f"🔄 Found {len(duplicate_records)} duplicate(s) for hash {pdf_hash[:16]}...")

                        return {
                            'success': True,
                            'record_id': record_id,
                            'pdf_hash': pdf_hash,
                            'is_duplicate': is_duplicate,
                            'duplicate_count': len(duplicate_records) if is_duplicate else 0,
                            'processing_time': processing_time
                        }
                    else:
                        # Mark as failed in database
                        self.mark_record_failed(record_id, table_name, f"Hash calculated but update failed: {update_error}")

                        with self.stats_lock:
                            self.stats['failed'] += 1
                        return {
                            'success': False,
                            'record_id': record_id,
                            'reason': f"Hash calculated but update failed: {update_error}"
                        }
                else:
                    # Mark as failed in database
                    self.mark_record_failed(record_id, table_name, error)

                    with self.stats_lock:
                        self.stats['failed'] += 1
                        # Track specific error types
                        if "404" in error:
                            self.stats['pdf_404_errors'] += 1
                        elif "not a valid PDF" in error or "HTML instead of PDF" in error:
                            self.stats['pdf_invalid_format'] += 1
                        elif "encrypted" in error:
                            self.stats['pdf_encrypted'] += 1
                        elif "No text extracted" in error:
                            self.stats['pdf_no_text'] += 1
                    return {
                        'success': False,
                        'record_id': record_id,
                        'reason': error
                    }

            # Edge Function processing mode
            else:
                edge_function_url = f"{self.supabase_url}/functions/v1/pdf-duplicate-detector"

                payload = {
                    "record_id": str(record_id),
                    "table_name": table_name,
                    "pdf_url": pdf_url
                }

                for attempt in range(max_retries):
                    try:
                        # Add delay between requests to avoid overwhelming the Edge Function
                        if attempt > 0:
                            delay = min(2 ** attempt, 30)  # Exponential backoff, max 30 seconds
                            print(f"⏳ Retrying {record_id} (attempt {attempt + 1}/{max_retries}) after {delay}s delay...")
                            time.sleep(delay)

                        response = requests.post(
                            edge_function_url,
                            headers=self.headers,
                            json=payload,
                            timeout=300  # 5 minutes timeout for PDF processing
                        )

                        # Break out of retry loop if successful
                        break

                    except requests.exceptions.Timeout:
                        if attempt == max_retries - 1:
                            raise
                        print(f"⏰ Timeout for {record_id}, retrying...")
                        continue
                    except requests.exceptions.RequestException as e:
                        if attempt == max_retries - 1:
                            raise
                        print(f"🔄 Request error for {record_id}: {str(e)[:100]}, retrying...")
                        continue

            if response.ok:
                result = response.json()
                if result.get('success'):
                    with self.stats_lock:
                        self.stats['successful'] += 1
                    return {
                        'success': True,
                        'record_id': record_id,
                        'pdf_hash': result.get('pdf_hash'),
                        'is_duplicate': result.get('is_duplicate'),
                        'processing_time': result.get('processing_time_ms')
                    }
                else:
                    with self.stats_lock:
                        self.stats['failed'] += 1
                    return {
                        'success': False,
                        'record_id': record_id,
                        'reason': result.get('error', 'Unknown error')
                    }
            else:
                # Handle specific error codes
                if response.status_code == 546:
                    error_msg = "Edge Function resource limit exceeded"
                elif response.status_code >= 500:
                    error_msg = f"Server error {response.status_code}"
                else:
                    error_msg = f'HTTP {response.status_code}: {response.text[:100]}'

                with self.stats_lock:
                    self.stats['failed'] += 1
                return {
                    'success': False,
                    'record_id': record_id,
                    'reason': error_msg
                }
                
        except Exception as e:
            with self.stats_lock:
                self.stats['failed'] += 1
            return {
                'success': False,
                'record_id': record.get('id', 'unknown'),
                'reason': str(e)
            }
    
    def process_table(self, table_name, max_workers=1, batch_size=10, test_limit=None, pending_only=False):
        """Process all records in a table"""
        print(f"\n🚀 Processing {table_name}...")
        print("=" * 60)

        # Get records needing hashes
        status_filter = 'pending' if pending_only else 'all'
        all_records = self.get_records_needing_hashes(table_name, limit=test_limit, status_filter=status_filter)
        
        if not all_records:
            print(f"✅ No records need hash calculation in {table_name}")
            return
        
        print(f"📊 Processing {len(all_records)} records with {max_workers} workers")
        
        # Process in batches to avoid overwhelming the system
        total_batches = (len(all_records) + batch_size - 1) // batch_size
        
        for batch_num in range(total_batches):
            start_idx = batch_num * batch_size
            end_idx = min(start_idx + batch_size, len(all_records))
            batch_records = all_records[start_idx:end_idx]
            
            print(f"\n📦 Processing batch {batch_num + 1}/{total_batches} ({len(batch_records)} records)")
            
            # Process batch with thread pool
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # Submit all tasks
                future_to_record = {
                    executor.submit(self.calculate_single_hash, record, table_name): record
                    for record in batch_records
                }
                
                # Process completed tasks
                completed = 0
                for future in as_completed(future_to_record):
                    record = future_to_record[future]
                    result = future.result()
                    completed += 1
                    
                    with self.stats_lock:
                        self.stats['total_processed'] += 1
                    
                    if result['success']:
                        print(f"✅ {completed}/{len(batch_records)} - {record['id']}: {result['pdf_hash'][:16]}... (duplicate: {result['is_duplicate']})")
                    else:
                        print(f"❌ {completed}/{len(batch_records)} - {record['id']}: {result['reason']}")
                    
                    # Print progress every 10 records
                    if completed % 10 == 0:
                        with self.stats_lock:
                            print(f"📊 Progress: {self.stats['total_processed']} total, {self.stats['successful']} successful, {self.stats['failed']} failed")
            
            # Small delay between batches
            if batch_num < total_batches - 1:
                print("⏸️ Waiting 2 seconds before next batch...")
                time.sleep(2)
        
        print(f"\n✅ Completed processing {table_name}")
    
    def process_all_tables(self, max_workers=1, batch_size=50, tables=None, test_limit=None, pending_only=False):
        """Process all tables or specified tables"""
        start_time = datetime.now()

        if tables is None:
            tables = ['bse_corporate_announcements', 'nse_corporate_announcements']

        mode_desc = "pending records" if pending_only else "all existing records"
        print(f"🚀 Starting PDF hash calculation for {mode_desc}")
        print("=" * 80)
        print(f"📅 Started at: {start_time}")
        print(f"📋 Tables to process: {', '.join(tables)}")
        print(f"⚙️ Max workers: {max_workers}")
        print(f"📦 Batch size: {batch_size}")
        if pending_only:
            print("🔄 Mode: Processing only pending records")
        print("=" * 80)

        for table_name in tables:
            # Apply test limit to get_records_needing_hashes if specified
            if test_limit:
                print(f"🧪 Test mode: limiting to {test_limit} records per table")
            self.process_table(table_name, max_workers, batch_size, test_limit, pending_only)
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        print("\n" + "=" * 80)
        print("🏁 PDF HASH CALCULATION COMPLETED")
        print("=" * 80)
        print(f"📅 Completed at: {end_time}")
        print(f"⏱️ Total duration: {duration}")
        print("📊 Final Statistics:")
        print(f"   Total processed: {self.stats['total_processed']}")
        print(f"   Successful: {self.stats['successful']}")
        print(f"   Failed: {self.stats['failed']}")
        print(f"   Skipped: {self.stats['skipped']}")

        # Show duplicate information
        if self.stats['duplicates_found'] > 0:
            print(f"   Duplicates found: {self.stats['duplicates_found']}")

        # Show breakdown of failure types
        if self.stats['failed'] > 0:
            print("   Failure breakdown:")
            if self.stats['pdf_404_errors'] > 0:
                print(f"     - PDF not found (404): {self.stats['pdf_404_errors']}")
            if self.stats['pdf_invalid_format'] > 0:
                print(f"     - Invalid PDF format: {self.stats['pdf_invalid_format']}")
            if self.stats['pdf_encrypted'] > 0:
                print(f"     - Encrypted PDFs: {self.stats['pdf_encrypted']}")
            if self.stats['pdf_no_text'] > 0:
                print(f"     - No text extracted: {self.stats['pdf_no_text']}")

        if self.stats['total_processed'] > 0:
            success_rate = (self.stats['successful'] / self.stats['total_processed']) * 100
            print(f"   Success rate: {success_rate:.2f}%")
        
        print("=" * 80)

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='Calculate PDF hashes for all existing records')
    parser.add_argument('--supabase-key', help='Supabase API key (optional, uses hardcoded key if not provided)')
    parser.add_argument('--max-workers', type=int, default=1, help='Maximum concurrent workers (default: 1)')
    parser.add_argument('--batch-size', type=int, default=10, help='Batch size for processing (default: 10)')
    parser.add_argument('--table', choices=['bse_corporate_announcements', 'nse_corporate_announcements'],
                       help='Process only specific table (default: both)')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be processed without actually processing')
    parser.add_argument('--local', action='store_true', help='Use local PDF processing instead of Edge Function (requires PyPDF2)')
    parser.add_argument('--test-limit', type=int, help='Limit processing to N records for testing (default: process all)')
    parser.add_argument('--skip-404', action='store_true', help='Skip processing records that are likely to return 404 errors')
    parser.add_argument('--pending-only', action='store_true', help='Process only records with pending duplicate_check_status')

    args = parser.parse_args()

    # Initialize calculator (use provided key or hardcoded key)
    calculator = PDFHashCalculator(args.supabase_key, use_local=args.local)

    if args.local:
        print("🏠 Using local PDF processing mode")
        if not PDF_SUPPORT:
            print("❌ PyPDF2 not available. Install with: pip install PyPDF2")
            sys.exit(1)
    else:
        print("☁️ Using Edge Function processing mode")
    
    try:
        if args.dry_run:
            print("🔍 DRY RUN MODE - Checking what would be processed...")
            tables = [args.table] if args.table else ['bse_corporate_announcements', 'nse_corporate_announcements']

            for table_name in tables:
                records = calculator.get_records_needing_hashes(table_name, limit=10)  # Limit for dry run
                print(f"📋 {table_name}: {len(records)} records would be processed")

                # Show sample URLs
                if records:
                    print(f"📄 Sample PDF URLs from {table_name}:")
                    for i, record in enumerate(records[:3]):  # Show first 3 URLs
                        if table_name == 'bse_corporate_announcements':
                            pdf_url = record.get('attachmentfile')
                        else:
                            pdf_url = record.get('attachment_url')
                        print(f"   {i+1}. {pdf_url}")
                    print()
        else:
            # Process tables
            tables = [args.table] if args.table else None
            calculator.process_all_tables(
                max_workers=args.max_workers,
                batch_size=args.batch_size,
                tables=tables,
                test_limit=args.test_limit,
                pending_only=args.pending_only
            )
            
    except KeyboardInterrupt:
        print("\n⚠️ Process interrupted by user")
        print(f"📊 Partial statistics: {calculator.stats}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

# Usage examples:
# python scripts/calculate_all_pdf_hashes.py  # Uses hardcoded API key
# python scripts/calculate_all_pdf_hashes.py --local  # Use local processing
# python scripts/calculate_all_pdf_hashes.py --local --pending-only  # Process only pending records
# python scripts/calculate_all_pdf_hashes.py --local --test-limit 10  # Test with limited records
# python scripts/calculate_all_pdf_hashes.py --local --table nse_corporate_announcements
# python scripts/calculate_all_pdf_hashes.py --dry-run  # See what would be processed
# python scripts/calculate_all_pdf_hashes.py --local --batch-size 20 --pending-only
