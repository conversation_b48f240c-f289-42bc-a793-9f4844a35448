"""
Shared CLI Framework for NSE and BSE Scrapers

This module provides a unified command-line interface framework that both scrapers
can inherit from, reducing code duplication and ensuring consistent user experience.
"""

import argparse
import sys
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Optional, Dict, Any, List


class BaseArgumentParser(ABC):
    """
    Base class for creating consistent argument parsers across scrapers.
    
    This class provides common argument patterns while allowing exchange-specific
    customization through abstract methods.
    """
    
    def __init__(self, exchange_name: str, description: str):
        """
        Initialize the base argument parser.
        
        Args:
            exchange_name (str): Name of the exchange (e.g., "NSE", "BSE")
            description (str): Description for the CLI help
        """
        self.exchange_name = exchange_name
        self.description = description
        self.parser = None
    
    def create_parser(self) -> argparse.ArgumentParser:
        """
        Create the argument parser with common and exchange-specific arguments.
        
        Returns:
            argparse.ArgumentParser: Configured argument parser
        """
        self.parser = argparse.ArgumentParser(
            description=self.description,
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog=self._get_examples()
        )
        
        # Add common arguments
        self._add_common_arguments()
        
        # Add exchange-specific arguments
        self._add_exchange_specific_arguments()
        
        return self.parser
    
    def _add_common_arguments(self):
        """Add arguments common to both scrapers"""
        # Date range arguments
        self.parser.add_argument(
            '--days',
            type=int,
            default=7,
            help='Number of days to scrape from today (default: 7)'
        )
        
        self.parser.add_argument(
            '--from',
            dest='from_date',
            help=f'Start date in {self._get_date_format()} format'
        )
        
        self.parser.add_argument(
            '--to',
            dest='to_date',
            help=f'End date in {self._get_date_format()} format'
        )
        
        # Output arguments
        self.parser.add_argument(
            '--output',
            help='Custom output filename (will add .csv extension if not present)'
        )
        
        self.parser.add_argument(
            '--summary-only',
            action='store_true',
            help='Show summary without saving data to files'
        )
        
        # Utility arguments
        self.parser.add_argument(
            '--list-files',
            action='store_true',
            help='List all saved files and exit'
        )

        # Proxy arguments
        self.parser.add_argument(
            '--no-proxy',
            action='store_true',
            help='Disable proxy usage (direct connection to exchange website)'
        )

        self.parser.add_argument(
            '--proxy',
            action='store_true',
            help='Force enable proxy usage (override configuration)'
        )
    
    @abstractmethod
    def _add_exchange_specific_arguments(self):
        """Add exchange-specific arguments (implemented by subclasses)"""
        pass
    
    @abstractmethod
    def _get_date_format(self) -> str:
        """Get the date format string for this exchange"""
        pass
    
    @abstractmethod
    def _get_examples(self) -> str:
        """Get example usage strings for this exchange"""
        pass


class BaseCommandExecutor(ABC):
    """
    Base class for executing commands consistently across scrapers.
    
    This class provides common command execution patterns while allowing
    exchange-specific implementation through abstract methods.
    """
    
    def __init__(self, exchange_name: str):
        """
        Initialize the command executor.
        
        Args:
            exchange_name (str): Name of the exchange
        """
        self.exchange_name = exchange_name
    
    def execute_command(self, args: argparse.Namespace):
        """
        Main command execution entry point.
        
        Args:
            args (argparse.Namespace): Parsed command line arguments
        """
        self._show_startup_message()
        
        try:
            # Handle utility commands first
            if args.list_files:
                self._handle_list_files_command(args)
                return
            
            # Execute main scraping logic
            with self._create_scraper(args) as scraper:
                result = self._perform_scraping(scraper, args)
                
                if result:
                    success = self._display_results(scraper, result, args)
                    if not success:
                        sys.exit(1)
                else:
                    print("❌ Failed to fetch data. Check the error messages above.")
                    sys.exit(1)
            
            print(f"\n🎉 Process completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        except KeyboardInterrupt:
            print("\n⏹️ Process interrupted by user")
            sys.exit(1)
        
        except Exception as e:
            print(f"\n❌ Unexpected error: {e}")
            import traceback
            traceback.print_exc()
            sys.exit(1)
    
    def _show_startup_message(self):
        """Display startup message"""
        print(f"🏢 {self.exchange_name} Insider Trading Scraper")
        print("=" * 50)
        print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
    
    @abstractmethod
    def _create_scraper(self, args=None):
        """Create and return the appropriate scraper instance"""
        pass
    
    @abstractmethod
    def _handle_list_files_command(self, args: argparse.Namespace):
        """Handle the --list-files command"""
        pass
    
    @abstractmethod
    def _perform_scraping(self, scraper, args: argparse.Namespace) -> Optional[Dict[str, Any]]:
        """Perform the actual scraping based on arguments"""
        pass
    
    @abstractmethod
    def _display_results(self, scraper, result: Dict[str, Any], args: argparse.Namespace) -> bool:
        """Display scraping results and handle data saving"""
        pass


class DataDisplayHelper:
    """
    Helper class for consistent data display across scrapers.
    """
    
    @staticmethod
    def display_summary(dataframe, exchange_name: str, data_type: str = "insider trading"):
        """
        Display a summary of the scraped data.
        
        Args:
            dataframe: pandas DataFrame with scraped data
            exchange_name (str): Name of the exchange
            data_type (str): Type of data (e.g., "insider trading", "corporate announcements")
        """
        if dataframe is None or len(dataframe) == 0:
            print(f"📊 No {data_type} data found")
            return
        
        print(f"\n📊 {exchange_name} {data_type.title()} Data Summary:")
        print("-" * 50)
        print(f"Total records: {len(dataframe)}")
        
        # Show column information
        print(f"Columns: {len(dataframe.columns)}")
        
        # Show sample data if available
        if len(dataframe) > 0:
            print(f"\n📄 Sample data (first 3 records):")
            # Limit columns and width for readability
            sample_data = dataframe.head(3)
            max_cols = min(5, len(sample_data.columns))
            print(sample_data.iloc[:, :max_cols].to_string(max_colwidth=20))
            
            if len(dataframe.columns) > max_cols:
                print(f"... and {len(dataframe.columns) - max_cols} more columns")
    
    @staticmethod
    def display_file_list(files: List[Dict[str, Any]], exchange_name: str):
        """
        Display a list of saved files.
        
        Args:
            files (List[Dict]): List of file information dictionaries
            exchange_name (str): Name of the exchange
        """
        if not files:
            print(f"📁 No saved {exchange_name} files found")
            return
        
        print(f"📁 Saved {exchange_name} files:")
        print("-" * 40)
        
        for file_info in files:
            print(f"📄 {file_info['name']}")
            print(f"   Size: {file_info.get('size_kb', 'Unknown')} KB")
            print(f"   Path: {file_info['path']}")
            print()


class ValidationHelper:
    """
    Helper class for common validation tasks across scrapers.
    """
    
    @staticmethod
    def validate_date_arguments(args: argparse.Namespace) -> bool:
        """
        Validate date-related command line arguments.
        
        Args:
            args (argparse.Namespace): Parsed arguments
            
        Returns:
            bool: True if validation passes
        """
        # Check if both from_date and to_date are provided together
        if (args.from_date and not args.to_date) or (args.to_date and not args.from_date):
            print("❌ Error: Both --from and --to dates must be provided together")
            return False
        
        # Check if days argument conflicts with date range
        if args.from_date and args.to_date and args.days != 7:  # 7 is default
            print("⚠️ Warning: --days argument ignored when using custom date range")

        return True

    @staticmethod
    def validate_proxy_arguments(args: argparse.Namespace) -> bool:
        """
        Validate proxy-related command line arguments.

        Args:
            args (argparse.Namespace): Parsed arguments

        Returns:
            bool: True if validation passes
        """
        # Check if both --proxy and --no-proxy are provided
        if getattr(args, 'proxy', False) and getattr(args, 'no_proxy', False):
            print("❌ Error: Cannot use both --proxy and --no-proxy arguments together")
            return False

        return True
    
    @staticmethod
    def validate_output_filename(filename: Optional[str]) -> Optional[str]:
        """
        Validate and normalize output filename.
        
        Args:
            filename (Optional[str]): User-provided filename
            
        Returns:
            Optional[str]: Validated filename or None if invalid
        """
        if not filename:
            return None
        
        # Ensure .csv extension
        if not filename.lower().endswith('.csv'):
            filename += '.csv'
        
        # Basic filename validation
        invalid_chars = ['<', '>', ':', '"', '|', '?', '*']
        for char in invalid_chars:
            if char in filename:
                print(f"❌ Error: Invalid character '{char}' in filename")
                return None
        
        return filename
